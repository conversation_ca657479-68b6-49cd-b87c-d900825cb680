# DeepSite Local Development Setup (No OAuth Required)

This guide shows you how to run DeepSite locally without needing Hugging Face OAuth authentication.

## Prerequisites

1. **Hugging Face Account & Token**
   - Create a free account at [huggingface.co](https://huggingface.co)
   - Go to [Settings > Access Tokens](https://huggingface.co/settings/tokens)
   - Create a new token with "Read" permissions (you don't need write permissions for basic AI functionality)

## Setup Steps

### 1. Environment Configuration

Create a `.env.local` file in the project root:

```bash
# Required: Your Hugging Face token for AI functionality
HF_TOKEN=hf_your_token_here

# Optional: Fallback token (can be the same as HF_TOKEN)
DEFAULT_HF_TOKEN=hf_your_token_here

# Optional: MongoDB for project saving (not required for basic AI functionality)
# MONGODB_URI=mongodb://localhost:27017/deepsite
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Run the Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## How It Works Without OAuth

The application has been modified to work locally without authentication:

1. **AI API Bypass**: When `HF_TOKEN` is set in environment variables, the AI API routes use this token instead of requiring user authentication.

2. **Login Modal Skip**: On localhost, the login modal automatically sets a fake user instead of redirecting to Hugging Face OAuth.

3. **Authentication Checks Disabled**: Authentication checks are bypassed for localhost.

## What Works Without Authentication

✅ **AI Code Generation**: Full AI functionality using your HF_TOKEN
✅ **Code Editor**: Monaco editor with syntax highlighting  
✅ **Live Preview**: Real-time website preview
✅ **Responsive Testing**: Device simulation
✅ **Follow-up Requests**: Iterative code modifications

## What Doesn't Work Without Authentication

❌ **Project Saving**: Requires MongoDB and user authentication
❌ **Hugging Face Spaces Deployment**: Requires OAuth for repo access
❌ **Project Management**: Loading/saving projects from database

## Troubleshooting

### "Login Required" Error
- Make sure `HF_TOKEN` is set in `.env.local`
- Verify your token is valid at [huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
- Restart the development server after adding the token

### AI Not Responding
- Check that your HF_TOKEN has sufficient credits
- Try a different AI provider in the settings
- Check the browser console for error messages

### Rate Limiting
- Without authentication, you're limited to 2 requests per IP
- With `HF_TOKEN`, this limit is bypassed

## Docker Setup (Alternative)

If you prefer Docker:

```bash
# Build the image
docker build -t deepsite .

# Run with environment variables
docker run -p 3000:3000 -e HF_TOKEN=your_token_here deepsite
```

## Getting Your Hugging Face Token

1. Go to [huggingface.co](https://huggingface.co) and sign up/login
2. Navigate to [Settings > Access Tokens](https://huggingface.co/settings/tokens)
3. Click "New token"
4. Give it a name like "DeepSite Local"
5. Select "Read" permissions
6. Copy the token (starts with `hf_`)
7. Add it to your `.env.local` file

That's it! You can now use DeepSite locally without any OAuth setup.
